"""
交易日历计算模块
提供准确的交易日计算功能，支持年化成本计算
"""
import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Set
import pandas as pd
import efinance as ef
from concurrent.futures import ThreadPoolExecutor
import json
import os


class TradingCalendar:
    """交易日历管理器"""
    
    def __init__(self, cache_dir: str = "cache", max_workers: int = 2):
        self.cache_dir = cache_dir
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = logging.getLogger(__name__)
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
        
        # 内存缓存
        self._trading_days_cache: Dict[int, Set[str]] = {}
        self._cache_loaded_years: Set[int] = set()
        
        # 每年大致的交易日天数
        self.trading_days_per_year = 243
    
    async def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        """获取指定日期范围内的交易日"""
        try:
            # 使用efinance获取交易日
            trading_days = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.trade_calendar.get_trade_days, start_date, end_date
            )
            
            self.logger.debug(f"获取交易日 {start_date} 到 {end_date}: {len(trading_days)} 天")
            return trading_days
            
        except Exception as e:
            self.logger.error(f"获取交易日失败: {e}")
            # 使用缓存或估算
            return await self._estimate_trading_days(start_date, end_date)
    
    async def get_remaining_trading_days(self, target_date: date) -> int:
        """计算从今天到目标日期的剩余交易日天数"""
        today = datetime.now().date()
        
        if target_date <= today:
            return 0
        
        start_str = today.strftime('%Y%m%d')
        end_str = target_date.strftime('%Y%m%d')
        
        trading_days = await self.get_trading_days(start_str, end_str)
        return len(trading_days)
    
    async def get_trading_days_between_contracts(self, date1: date, date2: date) -> int:
        """计算两个合约到期日之间的交易日天数"""
        if date1 >= date2:
            return 0
        
        start_str = date1.strftime('%Y%m%d')
        end_str = date2.strftime('%Y%m%d')
        
        trading_days = await self.get_trading_days(start_str, end_str)
        return len(trading_days)
    
    async def is_trading_day(self, check_date: date) -> bool:
        """检查指定日期是否为交易日"""
        year = check_date.year
        
        # 确保该年份的交易日已加载
        await self._load_trading_days_for_year(year)
        
        if year in self._trading_days_cache:
            date_str = check_date.strftime('%Y%m%d')
            return date_str in self._trading_days_cache[year]
        
        # 如果缓存中没有，使用简单规则估算
        return self._is_likely_trading_day(check_date)
    
    async def get_next_trading_day(self, from_date: date) -> Optional[date]:
        """获取指定日期之后的下一个交易日"""
        current_date = from_date + timedelta(days=1)
        max_check_days = 10  # 最多检查10天
        
        for _ in range(max_check_days):
            if await self.is_trading_day(current_date):
                return current_date
            current_date += timedelta(days=1)
        
        return None
    
    async def get_previous_trading_day(self, from_date: date) -> Optional[date]:
        """获取指定日期之前的上一个交易日"""
        current_date = from_date - timedelta(days=1)
        max_check_days = 10  # 最多检查10天
        
        for _ in range(max_check_days):
            if await self.is_trading_day(current_date):
                return current_date
            current_date -= timedelta(days=1)
        
        return None
    
    async def get_trading_days_in_year(self, year: int) -> List[str]:
        """获取指定年份的所有交易日"""
        await self._load_trading_days_for_year(year)
        
        if year in self._trading_days_cache:
            return sorted(list(self._trading_days_cache[year]))
        
        return []
    
    async def calculate_annualized_factor(self, days: int) -> float:
        """计算年化因子"""
        if days <= 0:
            return 0
        
        return self.trading_days_per_year / days
    
    async def _load_trading_days_for_year(self, year: int):
        """加载指定年份的交易日数据"""
        if year in self._cache_loaded_years:
            return
        
        # 先尝试从本地缓存加载
        cache_file = os.path.join(self.cache_dir, f"trading_days_{year}.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    trading_days = json.load(f)
                    self._trading_days_cache[year] = set(trading_days)
                    self._cache_loaded_years.add(year)
                    self.logger.debug(f"从缓存加载 {year} 年交易日数据")
                    return
            except Exception as e:
                self.logger.warning(f"加载交易日缓存失败: {e}")
        
        # 从API获取
        try:
            start_date = f"{year}0101"
            end_date = f"{year}1231"
            
            trading_days = await self.get_trading_days(start_date, end_date)
            
            if trading_days:
                self._trading_days_cache[year] = set(trading_days)
                self._cache_loaded_years.add(year)
                
                # 保存到缓存文件
                try:
                    with open(cache_file, 'w', encoding='utf-8') as f:
                        json.dump(trading_days, f)
                    self.logger.debug(f"缓存 {year} 年交易日数据")
                except Exception as e:
                    self.logger.warning(f"保存交易日缓存失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"获取 {year} 年交易日数据失败: {e}")
    
    async def _estimate_trading_days(self, start_date: str, end_date: str) -> List[str]:
        """估算交易日（当API不可用时的备用方案）"""
        try:
            start = datetime.strptime(start_date, '%Y%m%d').date()
            end = datetime.strptime(end_date, '%Y%m%d').date()
            
            trading_days = []
            current = start
            
            while current <= end:
                if self._is_likely_trading_day(current):
                    trading_days.append(current.strftime('%Y%m%d'))
                current += timedelta(days=1)
            
            self.logger.warning(f"使用估算方法计算交易日: {len(trading_days)} 天")
            return trading_days
            
        except Exception as e:
            self.logger.error(f"估算交易日失败: {e}")
            return []
    
    def _is_likely_trading_day(self, check_date: date) -> bool:
        """简单规则判断是否可能为交易日"""
        # 周末不是交易日
        if check_date.weekday() >= 5:  # 5=周六, 6=周日
            return False
        
        # 简单的节假日判断（可以扩展）
        month = check_date.month
        day = check_date.day
        
        # 元旦
        if month == 1 and day <= 3:
            return False
        
        # 春节期间（简化判断）
        if month == 2 and day <= 15:
            return False
        
        # 清明节期间
        if month == 4 and 3 <= day <= 6:
            return False
        
        # 劳动节
        if month == 5 and 1 <= day <= 5:
            return False
        
        # 国庆节
        if month == 10 and 1 <= day <= 7:
            return False
        
        return True
    
    async def get_statistics(self) -> Dict:
        """获取交易日历统计信息"""
        current_year = datetime.now().year
        
        # 获取当年交易日
        trading_days_this_year = await self.get_trading_days_in_year(current_year)
        
        # 计算已过交易日
        today = datetime.now().date()
        passed_trading_days = 0
        
        for day_str in trading_days_this_year:
            day_date = datetime.strptime(day_str, '%Y%m%d').date()
            if day_date < today:
                passed_trading_days += 1
        
        remaining_trading_days = len(trading_days_this_year) - passed_trading_days
        
        return {
            'current_year': current_year,
            'total_trading_days_this_year': len(trading_days_this_year),
            'passed_trading_days': passed_trading_days,
            'remaining_trading_days': remaining_trading_days,
            'trading_days_per_year': self.trading_days_per_year,
            'cached_years': list(self._cache_loaded_years)
        }
    
    async def clear_cache(self):
        """清空缓存"""
        self._trading_days_cache.clear()
        self._cache_loaded_years.clear()
        
        # 删除缓存文件
        try:
            for file in os.listdir(self.cache_dir):
                if file.startswith('trading_days_') and file.endswith('.json'):
                    os.remove(os.path.join(self.cache_dir, file))
            self.logger.info("交易日历缓存已清空")
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
    
    async def close(self):
        """关闭资源"""
        self.executor.shutdown(wait=True)


# 全局交易日历实例
_global_calendar: Optional[TradingCalendar] = None


def get_trading_calendar() -> TradingCalendar:
    """获取全局交易日历实例"""
    global _global_calendar
    if _global_calendar is None:
        _global_calendar = TradingCalendar()
    return _global_calendar
