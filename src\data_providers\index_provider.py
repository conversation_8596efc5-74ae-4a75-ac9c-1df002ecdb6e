"""
现货指数数据提供者实现
支持efinance和adata双数据源
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional
import pandas as pd
import efinance as ef
from concurrent.futures import ThreadPoolExecutor

from ..models.data_models import IndexQuote


class IndexDataProvider:
    """现货指数数据提供者"""
    
    def __init__(self, max_workers: int = 4, use_adata_fallback: bool = True):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = logging.getLogger(__name__)
        self.use_adata_fallback = use_adata_fallback
        
        # 尝试导入adata
        self.adata_available = False
        if use_adata_fallback:
            try:
                import adata
                self.adata = adata
                self.adata_available = True
                self.logger.info("adata模块可用，将作为备用数据源")
            except ImportError:
                self.logger.warning("adata模块不可用，仅使用efinance数据源")
    
    async def get_index_quotes_efinance(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """使用efinance获取指数行情"""
        result = {}

        # 指数代码映射（efinance的正确格式）
        index_code_mapping = {
            'sh000300': '1.000300',  # 沪深300
            'sh000905': '1.000905',  # 中证500
            'sh000852': '1.000852',  # 中证1000
            'sh000016': '1.000016',  # 上证50
            'sh000001': '1.000001',  # 上证指数
            'sz399001': '0.399001',  # 深证成指
            'sz399006': '0.399006',  # 创业板指
        }

        for index_code in indices:
            try:
                # 使用正确的efinance指数代码
                efinance_code = index_code_mapping.get(index_code)
                if not efinance_code:
                    self.logger.warning(f"未找到指数代码映射: {index_code}")
                    continue

                # 获取最近几天的数据
                from datetime import datetime, timedelta
                end_date = datetime.now().strftime('%Y%m%d')
                start_date = (datetime.now() - timedelta(days=5)).strftime('%Y%m%d')

                history_df = await asyncio.get_event_loop().run_in_executor(
                    self.executor, ef.stock.get_quote_history, efinance_code, start_date, end_date
                )

                if not history_df.empty:
                    # 获取最新的一条记录
                    latest = history_df.iloc[-1]

                    # 指数名称映射
                    index_name_mapping = {
                        'sh000300': '沪深300',
                        'sh000905': '中证500',
                        'sh000852': '中证1000',
                        'sh000016': '上证50',
                        'sh000001': '上证指数',
                        'sz399001': '深证成指',
                        'sz399006': '创业板指'
                    }

                    index_name = index_name_mapping.get(index_code, latest.get('股票名称', f'指数{index_code}'))

                    # 调试：查看获取到的数据
                    print(f"调试 - {index_code} ({efinance_code}) 数据列名: {latest.index.tolist()}")
                    print(f"调试 - {index_code} 最新数据: {latest.to_dict()}")

                    quote = IndexQuote(
                        index_code=index_code,
                        index_name=index_name,
                        timestamp=datetime.now(),
                        current_price=float(latest.get('收盘', 0)),
                        open_price=float(latest.get('开盘', 0)),
                        high_price=float(latest.get('最高', 0)),
                        low_price=float(latest.get('最低', 0)),
                        volume=int(latest.get('成交量', 0)),
                        amount=float(latest.get('成交额', 0)),
                        prev_close=float(latest.get('收盘', 0)),  # 使用当前收盘价作为昨收
                        change_pct=float(latest.get('涨跌幅', 0)),
                        change_amount=float(latest.get('涨跌额', 0))
                    )
                    result[index_code] = quote

            except Exception as e:
                self.logger.warning(f"获取指数 {index_code} 行情失败: {e}")
                continue

        self.logger.info(f"efinance获取到 {len(result)} 个指数行情")
        return result
    
    async def get_index_quotes_adata(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """使用adata获取指数行情"""
        if not self.adata_available:
            return {}
        
        result = {}
        for index_code in indices:
            try:
                # 转换指数代码格式（去掉sh前缀）
                adata_code = index_code.replace('sh', '').replace('sz', '')
                
                # 获取单个指数行情
                quote_df = await asyncio.get_event_loop().run_in_executor(
                    self.executor, 
                    self.adata.stock.market.get_market_index_current,
                    adata_code
                )
                
                if not quote_df.empty:
                    row = quote_df.iloc[0]
                    
                    quote = IndexQuote(
                        index_code=index_code,
                        index_name=f"指数{adata_code}",  # adata可能不返回名称
                        timestamp=datetime.now(),
                        current_price=float(row.get('price', 0)),
                        open_price=float(row.get('open', 0)),
                        high_price=float(row.get('high', 0)),
                        low_price=float(row.get('low', 0)),
                        volume=int(row.get('volume', 0)),
                        amount=float(row.get('amount', 0)),
                        prev_close=0,  # adata可能不提供昨收价
                        change_pct=0,  # 需要计算
                        change_amount=0  # 需要计算
                    )
                    
                    # 计算涨跌幅和涨跌额
                    if quote.prev_close > 0:
                        quote.change_amount = quote.current_price - quote.prev_close
                        quote.change_pct = (quote.change_amount / quote.prev_close) * 100
                    
                    result[index_code] = quote
                    
            except Exception as e:
                self.logger.warning(f"adata获取指数 {index_code} 行情失败: {e}")
                continue
        
        self.logger.info(f"adata获取到 {len(result)} 个指数行情")
        return result
    
    async def get_index_quotes(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """获取指数行情（主方法，自动选择数据源）"""
        # 首先尝试使用efinance
        result = await self.get_index_quotes_efinance(indices)
        
        # 如果efinance获取失败或数据不完整，尝试使用adata补充
        if len(result) < len(indices) and self.adata_available:
            missing_indices = [idx for idx in indices if idx not in result]
            if missing_indices:
                self.logger.info(f"使用adata补充缺失的指数数据: {missing_indices}")
                adata_result = await self.get_index_quotes_adata(missing_indices)
                result.update(adata_result)
        
        return result
    
    async def get_all_index_codes(self) -> List[Dict[str, str]]:
        """获取所有可用的指数代码"""
        result = []
        
        # 从adata获取指数代码列表
        if self.adata_available:
            try:
                codes_df = await asyncio.get_event_loop().run_in_executor(
                    self.executor, self.adata.stock.info.all_index_code
                )
                
                for _, row in codes_df.iterrows():
                    result.append({
                        'index_code': row.get('index_code', ''),
                        'name': row.get('name', ''),
                        'source': 'adata'
                    })
                    
            except Exception as e:
                self.logger.error(f"获取adata指数代码列表失败: {e}")
        
        # 添加常用的指数代码
        common_indices = [
            {'index_code': 'sh000300', 'name': '沪深300', 'source': 'common'},
            {'index_code': 'sh000905', 'name': '中证500', 'source': 'common'},
            {'index_code': 'sh000852', 'name': '中证1000', 'source': 'common'},
            {'index_code': 'sh000016', 'name': '上证50', 'source': 'common'},
            {'index_code': 'sh000001', 'name': '上证指数', 'source': 'common'},
            {'index_code': 'sz399001', 'name': '深证成指', 'source': 'common'},
            {'index_code': 'sz399006', 'name': '创业板指', 'source': 'common'},
        ]
        
        result.extend(common_indices)
        return result
    
    async def get_trading_calendar_adata(self, year: int) -> List[Dict]:
        """使用adata获取交易日历"""
        if not self.adata_available:
            return []
        
        try:
            calendar_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, self.adata.stock.info.trade_calendar, year
            )
            
            result = []
            for _, row in calendar_df.iterrows():
                result.append({
                    'trade_date': row.get('trade_date'),
                    'trade_status': row.get('trade_status'),
                    'day_week': row.get('day_week')
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取adata交易日历失败: {e}")
            return []
    
    async def validate_data_sources(self) -> Dict[str, bool]:
        """验证数据源可用性"""
        status = {
            'efinance': False,
            'adata': False
        }
        
        # 测试efinance
        try:
            test_indices = ['sh000300']  # 沪深300
            result = await self.get_index_quotes_efinance(test_indices)
            status['efinance'] = len(result) > 0
        except Exception as e:
            self.logger.error(f"efinance数据源测试失败: {e}")
        
        # 测试adata
        if self.adata_available:
            try:
                result = await self.get_index_quotes_adata(['sh000300'])
                status['adata'] = len(result) > 0
            except Exception as e:
                self.logger.error(f"adata数据源测试失败: {e}")
        
        return status
    
    async def get_index_history(self, index_code: str, days: int = 30) -> pd.DataFrame:
        """获取指数历史数据"""
        try:
            # 使用efinance获取历史数据
            from datetime import timedelta
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            # efinance的股票历史数据接口
            history_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.stock.get_quote_history, index_code, start_date, end_date
            )
            
            return history_df
            
        except Exception as e:
            self.logger.error(f"获取指数历史数据失败: {e}")
            return pd.DataFrame()
    
    async def close(self):
        """关闭资源"""
        self.executor.shutdown(wait=True)
