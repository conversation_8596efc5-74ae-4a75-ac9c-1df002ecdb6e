"""
基于efinance API的数据提供者实现
"""
import asyncio
import logging
import time
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import efinance as ef
from concurrent.futures import ThreadPoolExecutor

from ..models.data_models import FuturesContract, FuturesQuote, IndexQuote
from ..core.architecture import DataProvider


class RateLimiter:
    """API请求频率限制器"""

    def __init__(self, max_calls: int = 1, time_window: float = 2.0):
        """
        初始化频率限制器

        Args:
            max_calls: 时间窗口内最大调用次数
            time_window: 时间窗口长度（秒）
        """
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self._lock = asyncio.Lock()

    async def acquire(self):
        """获取调用许可"""
        async with self._lock:
            now = time.time()

            # 清理过期的调用记录
            self.calls = [call_time for call_time in self.calls
                         if now - call_time < self.time_window]

            # 如果达到限制，等待
            if len(self.calls) >= self.max_calls:
                sleep_time = self.time_window - (now - self.calls[0]) + 0.1
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    # 重新清理
                    now = time.time()
                    self.calls = [call_time for call_time in self.calls
                                 if now - call_time < self.time_window]

            # 记录本次调用
            self.calls.append(now)


class RetryConfig:
    """重试配置"""

    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay

    def get_delay(self, attempt: int) -> float:
        """计算重试延迟时间（指数退避）"""
        delay = self.base_delay * (2 ** attempt)
        return min(delay, self.max_delay)


async def retry_with_backoff(func, retry_config: RetryConfig, logger, operation_name: str):
    """带重试的异步函数执行器"""
    last_exception = None

    for attempt in range(retry_config.max_retries + 1):
        try:
            return await func()
        except Exception as e:
            last_exception = e

            # 检查是否是可重试的错误
            error_msg = str(e).lower()
            is_retryable = any(keyword in error_msg for keyword in [
                'connection', 'timeout', 'network', 'remote end closed',
                'max retries exceeded', 'protocol error'
            ])

            if attempt < retry_config.max_retries and is_retryable:
                delay = retry_config.get_delay(attempt)
                logger.warning(f"{operation_name} 失败 (尝试 {attempt + 1}/{retry_config.max_retries + 1}): {e}")
                logger.info(f"等待 {delay:.1f} 秒后重试...")
                await asyncio.sleep(delay)
            else:
                break

    # 所有重试都失败了
    logger.error(f"{operation_name} 最终失败: {last_exception}")
    raise last_exception


class EfinanceProvider:
    """efinance数据提供者"""

    def __init__(self, max_workers: int = 4, trading_calendar=None):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = logging.getLogger(__name__)

        # 交易日历引用
        self.trading_calendar = trading_calendar

        # API频率限制器 - 每2秒最多1次调用
        self.rate_limiter = RateLimiter(max_calls=1, time_window=2.0)

        # 重试配置
        self.retry_config = RetryConfig(max_retries=3, base_delay=2.0, max_delay=30.0)

        # 缓存期货基础信息
        self._futures_base_info: Optional[pd.DataFrame] = None
        self._last_base_info_update: Optional[datetime] = None
        self._base_info_cache_duration = 3600  # 1小时缓存

        # 缓存实时行情数据
        self._realtime_quotes: Optional[pd.DataFrame] = None
        self._last_quotes_update: Optional[datetime] = None
        self._quotes_cache_duration = 30  # 30秒缓存

        # 缓存合约和行情的组合数据
        self._combined_data_cache: Optional[tuple] = None
        self._last_combined_update: Optional[datetime] = None
        self._combined_cache_duration = 30  # 30秒缓存

        # 缓存指数行情数据
        self._index_quotes: Optional[pd.DataFrame] = None
        self._last_index_update: Optional[datetime] = None
        self._index_cache_duration = 30  # 30秒缓存

    async def get_all_futures_data(self, product_codes: List[str]) -> Tuple[List[FuturesContract], Dict[str, FuturesQuote]]:
        """一次性获取期货合约信息和行情数据，避免重复API调用"""
        now = datetime.now()

        # 检查组合缓存是否有效
        if (self._combined_data_cache is not None and
            self._last_combined_update is not None and
            (now - self._last_combined_update).total_seconds() < self._combined_cache_duration):
            self.logger.debug("使用缓存的组合数据")
            return self._combined_data_cache

        try:
            # 获取基础信息
            base_info = await self._get_futures_base_info()
            if base_info is None:
                return [], {}

            # 获取实时行情（只调用一次API）
            quotes_df = await self._get_realtime_quotes_cached()
            if quotes_df is None:
                return [], {}

            self.logger.info(f"获取到 {len(quotes_df)} 个期货行情数据")

            contracts = []
            quotes = {}

            for product_code in product_codes:
                self.logger.info(f"处理品种: {product_code}")

                # 筛选中金所的合约
                cffex_contracts = quotes_df[quotes_df['市场类型'] == '中金所'].copy()

                if cffex_contracts.empty:
                    self.logger.warning("未找到中金所合约")
                    continue

                # 根据期货名称筛选对应品种的合约
                product_name_mapping = {
                    'IC': ['中证500', 'IC'],
                    'IM': ['中证1000', 'IM'],
                    'IF': ['沪深300', 'IF'],
                    'IH': ['上证50', 'IH']
                }

                if product_code in product_name_mapping:
                    name_patterns = product_name_mapping[product_code]

                    # 使用名称模式筛选
                    product_contracts = pd.DataFrame()
                    for pattern in name_patterns:
                        matches = cffex_contracts[
                            cffex_contracts['期货名称'].str.contains(pattern, na=False, case=False)
                        ]
                        product_contracts = pd.concat([product_contracts, matches])

                    # 去重
                    product_contracts = product_contracts.drop_duplicates(subset=['期货代码'])

                    if product_contracts.empty:
                        self.logger.warning(f"未找到 {product_code} 品种的合约")
                        continue

                    self.logger.info(f"找到 {len(product_contracts)} 个 {product_code} 合约")

                    # 按成交量排序
                    product_contracts = product_contracts.sort_values('成交量', ascending=False)

                    # 计算品种总成交量（去重处理）
                    total_volume = self._calculate_total_volume(product_contracts)

                    # 获取所有合约（不限制数量）
                    for idx, row in product_contracts.iterrows():
                        contract_code = row['期货代码']

                        # 从基础信息中获取详细信息
                        base_row = base_info[base_info['期货代码'] == contract_code]

                        quote_id = row.get('行情ID', '')
                        market_type = row.get('市场类型', '')

                        if not base_row.empty:
                            quote_id = base_row.iloc[0].get('行情ID', quote_id)
                            market_type = base_row.iloc[0].get('市场类型', market_type)

                        # 判断是否为主力合约（成交量最大的）
                        is_main = idx == product_contracts.index[0]

                        # 解析到期日期和月份
                        current_price = float(row.get('最新价', 0))
                        expiry_date, contract_month = await self._parse_contract_info(
                            contract_code, row['期货名称'], current_price, product_contracts
                        )

                        # 计算交易量和占比
                        volume = int(row.get('成交量', 0))
                        volume_ratio = (volume / total_volume * 100) if total_volume > 0 else 0.0

                        # 创建合约对象
                        contract = FuturesContract(
                            contract_code=contract_code,
                            product_code=product_code,
                            contract_name=row.get('期货名称', ''),
                            quote_id=quote_id,
                            market_type=market_type,
                            expiry_date=expiry_date,
                            is_main_contract=is_main,
                            volume=volume,
                            volume_ratio=volume_ratio
                        )
                        contracts.append(contract)

                        # 同时创建行情对象
                        quote = FuturesQuote(
                            contract_code=contract_code,
                            timestamp=datetime.now(),
                            current_price=float(row.get('最新价', 0)),
                            open_price=float(row.get('今开', 0)),
                            high_price=float(row.get('最高', 0)),
                            low_price=float(row.get('最低', 0)),
                            volume=int(row.get('成交量', 0)),
                            amount=float(row.get('成交额', 0)),
                            prev_close=float(row.get('昨日收盘', 0)),
                            change_pct=float(row.get('涨跌幅', 0)),
                            change_amount=float(row.get('涨跌额', 0))
                        )
                        quotes[contract_code] = quote

                        self.logger.debug(f"添加合约: {contract_code} - {row['期货名称']} - 成交量: {row['成交量']}")

            self.logger.info(f"总共获取到 {len(contracts)} 个期货合约和 {len(quotes)} 个行情")

            # 更新组合缓存
            result = (contracts, quotes)
            self._combined_data_cache = result
            self._last_combined_update = now

            return result

        except Exception as e:
            self.logger.error(f"获取期货数据失败: {e}")
            import traceback
            traceback.print_exc()

            # 如果有缓存数据，返回缓存数据
            if self._combined_data_cache is not None:
                self.logger.warning("API调用失败，使用缓存的组合数据")
                return self._combined_data_cache

            return [], {}

    async def _get_realtime_quotes_cached(self) -> Optional[pd.DataFrame]:
        """获取实时行情数据（带缓存和频率控制）"""
        now = datetime.now()

        # 检查缓存是否有效
        if (self._realtime_quotes is not None and
            self._last_quotes_update is not None and
            (now - self._last_quotes_update).total_seconds() < self._quotes_cache_duration):
            self.logger.debug("使用缓存的实时行情数据")
            return self._realtime_quotes

        async def _fetch_quotes():
            # 应用频率限制
            await self.rate_limiter.acquire()

            self.logger.info("正在获取实时行情数据...")

            # 在线程池中执行同步API调用
            quotes_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.futures.get_realtime_quotes
            )

            # 更新缓存
            self._realtime_quotes = quotes_df
            self._last_quotes_update = now

            self.logger.info(f"成功获取 {len(quotes_df)} 个期货行情数据")
            return quotes_df

        try:
            # 使用重试机制获取数据
            return await retry_with_backoff(
                _fetch_quotes,
                self.retry_config,
                self.logger,
                "获取实时行情"
            )

        except Exception as e:
            self.logger.error(f"获取实时行情最终失败: {e}")
            # 如果获取失败但有缓存数据，返回缓存数据
            if self._realtime_quotes is not None:
                self.logger.warning("API调用失败，使用缓存数据")
                return self._realtime_quotes
            return None

    async def _get_index_quotes_cached(self) -> Optional[pd.DataFrame]:
        """获取指数行情数据（带缓存和频率控制）"""
        now = datetime.now()

        # 检查缓存是否有效
        if (self._index_quotes is not None and
            self._last_index_update is not None and
            (now - self._last_index_update).total_seconds() < self._index_cache_duration):
            self.logger.debug("使用缓存的指数行情数据")
            return self._index_quotes

        async def _fetch_index_quotes():
            # 应用频率限制
            await self.rate_limiter.acquire()

            self.logger.info("正在获取指数行情数据...")

            # 在线程池中执行同步API调用，获取所有沪深系列指数
            quotes_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.stock.get_realtime_quotes, '沪深系列指数'
            )

            # 更新缓存
            self._index_quotes = quotes_df
            self._last_index_update = now

            self.logger.info(f"成功获取 {len(quotes_df)} 个指数行情数据")
            return quotes_df

        try:
            # 使用重试机制获取数据
            return await retry_with_backoff(
                _fetch_index_quotes,
                self.retry_config,
                self.logger,
                "获取指数行情"
            )

        except Exception as e:
            self.logger.error(f"获取指数行情最终失败: {e}")
            # 如果获取失败但有缓存数据，返回缓存数据
            if self._index_quotes is not None:
                self.logger.warning("API调用失败，使用缓存的指数数据")
                return self._index_quotes
            return None

    async def get_futures_quotes(self, contracts: List[str]) -> Dict[str, FuturesQuote]:
        """获取期货行情"""
        try:
            # 使用缓存的实时行情数据
            quotes_df = await self._get_realtime_quotes_cached()

            if quotes_df is None:
                self.logger.error("无法获取实时行情数据")
                return {}

            result = {}
            for contract_code in contracts:
                # 查找对应的行情数据
                contract_data = quotes_df[quotes_df['期货代码'] == contract_code]
                if not contract_data.empty:
                    row = contract_data.iloc[0]

                    quote = FuturesQuote(
                        contract_code=contract_code,
                        timestamp=datetime.now(),
                        current_price=float(row.get('最新价', 0)),
                        open_price=float(row.get('今开', 0)),
                        high_price=float(row.get('最高', 0)),
                        low_price=float(row.get('最低', 0)),
                        volume=int(row.get('成交量', 0)),
                        amount=float(row.get('成交额', 0)),
                        prev_close=float(row.get('昨日收盘', 0)),
                        change_pct=float(row.get('涨跌幅', 0)),
                        change_amount=float(row.get('涨跌额', 0))
                    )
                    result[contract_code] = quote

            self.logger.info(f"获取到 {len(result)} 个期货合约行情")
            return result

        except Exception as e:
            self.logger.error(f"获取期货行情失败: {e}")
            return {}
    
    async def get_index_quotes(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """获取指数行情"""
        try:
            # 使用缓存的指数行情数据
            quotes_df = await self._get_index_quotes_cached()

            if quotes_df is None:
                self.logger.error("无法获取指数行情数据")
                return {}

            # 创建指数名称到代码的映射
            index_name_mapping = {
                '沪深300': 'sh000300',
                '中证500': 'sh000905',
                '中证1000': 'sh000852',
                '上证50': 'sh000016'
            }

            result = {}
            for _, row in quotes_df.iterrows():
                # 使用股票名称字段进行筛选
                stock_name = row.get('股票名称', '')

                # 检查是否是我们需要的指数
                if stock_name in index_name_mapping:
                    index_code = index_name_mapping[stock_name]

                    # 只处理请求的指数
                    if index_code in indices:
                        quote = IndexQuote(
                            index_code=index_code,
                            index_name=stock_name,
                            timestamp=datetime.now(),
                            current_price=float(row.get('最新价', 0)),
                            open_price=float(row.get('今开', 0)),
                            high_price=float(row.get('最高', 0)),
                            low_price=float(row.get('最低', 0)),
                            volume=int(row.get('成交量', 0)),
                            amount=float(row.get('成交额', 0)),
                            prev_close=float(row.get('昨日收盘', 0)),
                            change_pct=float(row.get('涨跌幅', 0)),
                            change_amount=float(row.get('涨跌额', 0))
                        )
                        result[index_code] = quote
                        self.logger.debug(f"找到指数: {stock_name} ({index_code}) - 价格: {row.get('最新价', 0)}")

            self.logger.info(f"获取到 {len(result)} 个指数行情")
            return result

        except Exception as e:
            self.logger.error(f"获取指数行情失败: {e}")
            return {}
    
    async def get_futures_contracts(self, product_codes: List[str]) -> List[FuturesContract]:
        """获取期货合约信息"""
        try:
            # 获取基础信息
            base_info = await self._get_futures_base_info()
            if base_info is None:
                return []

            # 获取实时行情以筛选活跃合约
            quotes_df = await self._get_realtime_quotes_cached()

            if quotes_df is None:
                self.logger.error("无法获取实时行情数据")
                return []

            self.logger.info(f"获取到 {len(quotes_df)} 个期货行情数据")

            contracts = []
            for product_code in product_codes:
                self.logger.info(f"处理品种: {product_code}")

                # 筛选中金所的合约
                cffex_contracts = quotes_df[quotes_df['市场类型'] == '中金所'].copy()

                if cffex_contracts.empty:
                    self.logger.warning("未找到中金所合约")
                    continue

                # 根据期货名称筛选对应品种的合约
                product_name_mapping = {
                    'IC': ['中证500', 'IC'],
                    'IM': ['中证1000', 'IM'],
                    'IF': ['沪深300', 'IF'],
                    'IH': ['上证50', 'IH']
                }

                if product_code in product_name_mapping:
                    name_patterns = product_name_mapping[product_code]

                    # 使用名称模式筛选
                    product_contracts = pd.DataFrame()
                    for pattern in name_patterns:
                        matches = cffex_contracts[
                            cffex_contracts['期货名称'].str.contains(pattern, na=False, case=False)
                        ]
                        product_contracts = pd.concat([product_contracts, matches])

                    # 去重
                    product_contracts = product_contracts.drop_duplicates(subset=['期货代码'])

                    if product_contracts.empty:
                        self.logger.warning(f"未找到 {product_code} 品种的合约")
                        continue

                    self.logger.info(f"找到 {len(product_contracts)} 个 {product_code} 合约")

                    # 按成交量排序
                    product_contracts = product_contracts.sort_values('成交量', ascending=False)

                    # 获取所有合约（不限制数量）
                    for idx, row in product_contracts.iterrows():
                        contract_code = row['期货代码']

                        # 从基础信息中获取详细信息
                        base_row = base_info[base_info['期货代码'] == contract_code]

                        quote_id = row.get('行情ID', '')
                        market_type = row.get('市场类型', '')

                        if not base_row.empty:
                            quote_id = base_row.iloc[0].get('行情ID', quote_id)
                            market_type = base_row.iloc[0].get('市场类型', market_type)

                        # 判断是否为主力合约（成交量最大的）
                        is_main = idx == product_contracts.index[0]

                        # 解析到期日期和月份
                        current_price = float(row.get('最新价', 0))
                        expiry_date, contract_month = await self._parse_contract_info(
                            contract_code, row['期货名称'], current_price, product_contracts
                        )

                        # 计算交易量和占比
                        volume = int(row.get('成交量', 0))
                        # 这里暂时设置为0，因为这个方法可能不需要计算占比
                        volume_ratio = 0.0

                        contract = FuturesContract(
                            contract_code=contract_code,
                            product_code=product_code,
                            contract_name=row.get('期货名称', ''),
                            quote_id=quote_id,
                            market_type=market_type,
                            expiry_date=expiry_date,
                            is_main_contract=is_main,
                            volume=volume,
                            volume_ratio=volume_ratio
                        )

                        contracts.append(contract)

                        self.logger.debug(f"添加合约: {contract_code} - {row['期货名称']} - 成交量: {row['成交量']}")

            self.logger.info(f"总共获取到 {len(contracts)} 个期货合约")
            return contracts

        except Exception as e:
            self.logger.error(f"获取期货合约信息失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历"""
        try:
            # efinance可能没有trade_calendar模块，使用简单的工作日计算
            from datetime import datetime, timedelta

            start = datetime.strptime(start_date, '%Y%m%d')
            end = datetime.strptime(end_date, '%Y%m%d')

            trading_days = []
            current = start

            while current <= end:
                # 简单的工作日判断（周一到周五）
                if current.weekday() < 5:  # 0-4 是周一到周五
                    trading_days.append(current.strftime('%Y%m%d'))
                current += timedelta(days=1)

            # 排除一些明显的节假日（简化处理）
            holidays = self._get_simple_holidays(start.year, end.year)
            trading_days = [day for day in trading_days if day not in holidays]

            return trading_days

        except Exception as e:
            self.logger.error(f"获取交易日历失败: {e}")
            return []

    def _get_simple_holidays(self, start_year: int, end_year: int) -> List[str]:
        """获取简单的节假日列表"""
        holidays = []

        for year in range(start_year, end_year + 1):
            # 元旦
            holidays.extend([f"{year}0101"])

            # 春节（简化为2月前两周）
            for day in range(1, 15):
                holidays.append(f"{year}02{day:02d}")

            # 清明节（4月4-6日）
            holidays.extend([f"{year}0404", f"{year}0405", f"{year}0406"])

            # 劳动节（5月1-3日）
            holidays.extend([f"{year}0501", f"{year}0502", f"{year}0503"])

            # 国庆节（10月1-7日）
            for day in range(1, 8):
                holidays.append(f"{year}10{day:02d}")

        return holidays
    
    async def get_quote_history(self, quote_id: str, days: int = 30) -> pd.DataFrame:
        """获取历史行情数据"""
        try:
            from datetime import timedelta
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            history_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.futures.get_quote_history, quote_id, start_date, end_date
            )
            
            return history_df
            
        except Exception as e:
            self.logger.error(f"获取历史行情失败: {e}")
            return pd.DataFrame()
    
    async def _get_futures_base_info(self) -> Optional[pd.DataFrame]:
        """获取期货基础信息（带缓存）"""
        now = datetime.now()
        
        # 检查缓存是否有效
        if (self._futures_base_info is not None and 
            self._last_base_info_update is not None and
            (now - self._last_base_info_update).seconds < self._base_info_cache_duration):
            return self._futures_base_info
        
        async def _fetch_base_info():
            # 应用频率限制
            await self.rate_limiter.acquire()

            # 重新获取基础信息
            base_info = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.futures.get_futures_base_info
            )

            self._futures_base_info = base_info
            self._last_base_info_update = now

            self.logger.info("更新期货基础信息缓存")
            return base_info

        try:
            # 使用重试机制获取数据
            return await retry_with_backoff(
                _fetch_base_info,
                self.retry_config,
                self.logger,
                "获取期货基础信息"
            )

        except Exception as e:
            self.logger.error(f"获取期货基础信息最终失败: {e}")
            return None
    
    def _parse_expiry_date(self, contract_code: str) -> Optional[date]:
        """从合约代码解析到期日期"""
        try:
            # 提取月份代码，如IC2407中的2407
            if len(contract_code) >= 6:
                month_code = contract_code[-4:]  # 如2407
                year = int("20" + month_code[:2])  # 2024
                month = int(month_code[2:])  # 07

                # 股指期货通常在第三个周五到期
                # 这里简化处理，设为当月第三个周五
                from calendar import monthrange
                import calendar

                # 找到当月第三个周五
                first_day_weekday = calendar.weekday(year, month, 1)
                first_friday = 1 + (4 - first_day_weekday) % 7
                third_friday = first_friday + 14

                # 确保不超过当月天数
                days_in_month = monthrange(year, month)[1]
                if third_friday > days_in_month:
                    third_friday -= 7

                return date(year, month, third_friday)

        except Exception as e:
            self.logger.warning(f"解析到期日期失败 {contract_code}: {e}")

        return None

    def _parse_expiry_date_numeric(self, contract_code: str) -> Optional[date]:
        """从数字格式期货代码解析到期日期"""
        try:
            # 数字格式如：151107 -> 15年11月07日
            # 或者：061107 -> 06年11月07日
            if len(contract_code) == 6:
                year_part = contract_code[:2]
                month_part = contract_code[2:4]
                day_part = contract_code[4:6]

                # 年份处理：如果是15-99，认为是20xx年；如果是00-14，认为是20xx年
                year_num = int(year_part)
                if year_num >= 0:  # 都认为是20xx年
                    year = 2000 + year_num
                else:
                    year = 2000 + year_num

                month = int(month_part)

                # 对于股指期货，通常在第三个周五到期，忽略day_part
                from calendar import monthrange
                import calendar

                # 找到当月第三个周五
                first_day_weekday = calendar.weekday(year, month, 1)
                first_friday = 1 + (4 - first_day_weekday) % 7
                third_friday = first_friday + 14

                # 确保不超过当月天数
                days_in_month = monthrange(year, month)[1]
                if third_friday > days_in_month:
                    third_friday -= 7

                return date(year, month, third_friday)

        except Exception as e:
            self.logger.warning(f"解析数字格式到期日期失败 {contract_code}: {e}")

        return None

    async def _parse_contract_info(self, contract_code: str, contract_name: str, price: float = None, all_contracts_data: pd.DataFrame = None) -> tuple:
        """解析合约信息，返回(到期日期, 合约月份)"""
        try:
            import re

            # 检查是否为连续合约
            continuous_patterns = ['当月连续', '下月连续', '下季连续', '隔季连续', '主力合约', '次主力合']
            is_continuous = any(pattern in contract_name for pattern in continuous_patterns)

            if is_continuous and price is not None and all_contracts_data is not None:
                # 通过价格匹配找到对应的具体合约
                matched_contract = self._find_matching_contract_by_price(price, all_contracts_data, contract_code)
                if matched_contract:
                    return await self._parse_contract_info(matched_contract['期货代码'], matched_contract['期货名称'])

            # 尝试从名称中提取年月信息，如"IC2507"
            month_match = re.search(r'([A-Z]+)(\d{4})', contract_name)
            if month_match:
                year_month = month_match.group(2)
                year = int("20" + year_month[:2])
                month = int(year_month[2:])

                # 计算到期日期（第三个周五，确保是交易日）
                expiry_date = await self._calculate_expiry_date(year, month)
                return expiry_date, year_month

            # 如果名称中没有找到，尝试从数字代码解析
            if len(contract_code) >= 6:
                # 提取后4位作为年月
                year_month_part = contract_code[-4:]
                if year_month_part.isdigit():
                    year = int("20" + year_month_part[:2])
                    month = int(year_month_part[2:])

                    expiry_date = await self._calculate_expiry_date(year, month)
                    return expiry_date, year_month_part

        except Exception as e:
            self.logger.warning(f"解析合约信息失败 {contract_code}: {e}")

        return None, "未知"

    def _calculate_total_volume(self, contracts_data: pd.DataFrame) -> int:
        """计算品种总成交量，处理重复合约"""
        try:
            # 通过价格分组来识别重复合约
            unique_volumes = {}

            for _, row in contracts_data.iterrows():
                price = float(row.get('最新价', 0))
                volume = int(row.get('成交量', 0))
                contract_name = row.get('期货名称', '')

                # 如果是连续合约，跳过（避免重复计算）
                if any(pattern in contract_name for pattern in ['连续', '主力', '次主力']):
                    continue

                # 使用价格作为键来去重
                price_key = round(price, 2)  # 保留2位小数作为键
                if price_key not in unique_volumes:
                    unique_volumes[price_key] = volume
                else:
                    # 如果价格相同，取较大的成交量
                    unique_volumes[price_key] = max(unique_volumes[price_key], volume)

            return sum(unique_volumes.values())

        except Exception as e:
            self.logger.warning(f"计算总成交量失败: {e}")
            return sum(int(row.get('成交量', 0)) for _, row in contracts_data.iterrows())

    def _find_matching_contract_by_price(self, target_price: float, all_contracts_data: pd.DataFrame, exclude_code: str) -> Optional[dict]:
        """通过价格匹配找到对应的具体合约"""
        try:
            # 筛选出具体月份合约（排除连续合约）
            specific_contracts = all_contracts_data[
                ~all_contracts_data['期货名称'].str.contains('连续|主力|次主力', na=False)
            ].copy()

            # 排除当前合约
            specific_contracts = specific_contracts[specific_contracts['期货代码'] != exclude_code]

            # 找到价格最接近的合约（允许小幅差异）
            price_diff_threshold = target_price * 0.001  # 0.1%的价格差异容忍度

            for _, row in specific_contracts.iterrows():
                contract_price = float(row.get('最新价', 0))
                if abs(contract_price - target_price) <= price_diff_threshold:
                    return row.to_dict()

            return None

        except Exception as e:
            self.logger.warning(f"价格匹配失败: {e}")
            return None

    async def _calculate_expiry_date(self, year: int, month: int) -> Optional[date]:
        """计算到期日期（第三个周五，确保是交易日）"""
        try:
            from calendar import monthrange
            import calendar

            # 找到当月第三个周五
            first_day_weekday = calendar.weekday(year, month, 1)
            first_friday = 1 + (4 - first_day_weekday) % 7
            third_friday = first_friday + 14

            # 确保不超过当月天数
            days_in_month = monthrange(year, month)[1]
            if third_friday > days_in_month:
                third_friday -= 7

            expiry_date = date(year, month, third_friday)

            # 检查是否为交易日，如果不是则向前查找
            if hasattr(self, 'trading_calendar') and self.trading_calendar:
                date_str = expiry_date.strftime('%Y%m%d')
                is_trading_day = await self.trading_calendar.is_trading_day(date_str)

                # 如果不是交易日，向前查找最近的交易日
                check_date = expiry_date
                max_check_days = 7  # 最多向前查找7天

                for _ in range(max_check_days):
                    if is_trading_day:
                        return check_date
                    check_date -= timedelta(days=1)
                    date_str = check_date.strftime('%Y%m%d')
                    is_trading_day = await self.trading_calendar.is_trading_day(date_str)

                # 如果向前7天都不是交易日，返回原始日期
                return expiry_date
            else:
                # 如果没有交易日历，返回原始计算的第三个周五
                return expiry_date

        except Exception as e:
            self.logger.warning(f"计算到期日期失败 {year}-{month}: {e}")
            return None

    def _calculate_third_friday(self, year: int, month: int) -> Optional[date]:
        """计算指定年月的第三个周五"""
        try:
            from calendar import monthrange
            import calendar

            # 找到当月第三个周五
            first_day_weekday = calendar.weekday(year, month, 1)
            first_friday = 1 + (4 - first_day_weekday) % 7
            third_friday = first_friday + 14

            # 确保不超过当月天数
            days_in_month = monthrange(year, month)[1]
            if third_friday > days_in_month:
                third_friday -= 7

            return date(year, month, third_friday)

        except Exception as e:
            self.logger.warning(f"计算第三个周五失败 {year}-{month}: {e}")
            return None

    async def close(self):
        """关闭资源"""
        self.executor.shutdown(wait=True)
