2025-07-07 10:33:52,059 - src.data_providers.index_provider - WARNING - adata模块不可用，仅使用efinance数据源
2025-07-07 10:33:52,060 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 10:33:52,060 - __main__ - INFO - 验证数据源可用性...
2025-07-07 10:33:52,061 - src.data_providers.index_provider - ERROR - efinance获取指数行情失败: "指定的行情参数 `['sh000300']` 不正确"
2025-07-07 10:33:52,061 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': False}
2025-07-07 10:33:52,062 - __main__ - ERROR - 系统运行错误: 所有数据源都不可用
2025-07-07 10:33:52,062 - __main__ - INFO - 正在停止监控系统...
2025-07-07 10:33:52,062 - __main__ - INFO - 监控系统已停止
2025-07-07 11:26:08,900 - src.data_providers.index_provider - WARNING - adata模块不可用，仅使用efinance数据源
2025-07-07 11:26:08,900 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 11:26:08,901 - __main__ - INFO - 验证数据源可用性...
2025-07-07 11:26:09,176 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 11:26:09,176 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': False}
2025-07-07 11:26:09,177 - __main__ - ERROR - 系统运行错误: 所有数据源都不可用
2025-07-07 11:26:09,177 - __main__ - INFO - 正在停止监控系统...
2025-07-07 11:26:09,177 - __main__ - INFO - 监控系统已停止
2025-07-07 12:00:54,459 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:00:54,460 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:00:54,460 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:00:54,777 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:00:54,815 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:00:54,815 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:00:54,815 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:00:54
2025-07-07 12:00:55,015 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:00:55,343 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:00:55,343 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:00:55,642 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:00:55,782 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:00:55,783 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:00:55,870 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:00:55,871 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:00:55,871 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,873 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:00:55,873 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,874 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:00:55,876 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,877 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:00:55,877 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,878 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:00:55,879 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,890 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:00:55,891 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,893 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:00:55,894 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:00:55,918 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:01:55,908 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:01:55
2025-07-07 12:01:56,113 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:01:56,114 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:01:56,273 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:01:56,467 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:01:56,467 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:01:56,563 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:01:56,563 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:01:56,563 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,564 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:01:56,564 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,565 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:01:56,565 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,566 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:01:56,567 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,568 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:01:56,568 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,569 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:01:56,569 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,570 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:01:56,570 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:01:56,586 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:02:56,589 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:02:56
2025-07-07 12:02:56,841 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:02:56,842 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:02:57,018 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:02:57,189 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:02:57,189 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:02:57,287 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:02:57,288 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:02:57,288 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,289 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:02:57,289 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,290 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:02:57,290 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,291 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:02:57,292 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,293 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:02:57,293 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,294 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:02:57,294 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,295 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:02:57,296 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:02:57,311 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:03:57,312 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:03:57
2025-07-07 12:03:57,701 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:03:57,701 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:03:57,946 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:03:58,129 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:03:58,130 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:03:58,570 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:03:58,571 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:03:58,571 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,572 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:03:58,572 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,573 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:03:58,573 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,574 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:03:58,575 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,576 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:03:58,576 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,577 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:03:58,579 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,579 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:03:58,580 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:03:58,596 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:04:58,608 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:04:58
2025-07-07 12:04:58,812 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:04:58,812 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:04:58,939 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:04:59,220 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:04:59,220 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:04:59,311 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:04:59,312 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:04:59,312 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,313 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:04:59,314 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,314 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:04:59,314 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,315 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:04:59,316 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,317 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:04:59,317 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,318 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:04:59,318 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,319 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:04:59,321 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:04:59,336 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:05:59,347 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:05:59
2025-07-07 12:05:59,731 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:05:59,732 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:06:00,004 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:06:00,166 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:06:00,167 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:06:00,571 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:06:00,571 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:06:00,572 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,573 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:06:00,573 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,574 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:06:00,574 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,576 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:06:00,576 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,578 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:06:00,578 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,579 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:06:00,579 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,580 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:06:00,580 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:06:00,596 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:07:00,606 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:07:00
2025-07-07 12:07:00,999 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:07:00,999 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:07:01,253 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:07:01,392 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:07:01,392 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:07:01,805 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:07:01,805 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:07:01,805 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,807 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:07:01,808 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,808 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:07:01,809 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,810 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:07:01,810 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,811 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:07:01,811 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,812 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:07:01,813 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,814 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:07:01,814 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:07:01,830 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:08:01,834 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:08:01
2025-07-07 12:08:02,067 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:08:02,068 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:08:02,249 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:08:02,416 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:08:02,416 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:08:02,505 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:08:02,506 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:08:02,506 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,507 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:08:02,507 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,508 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:08:02,508 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,509 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:08:02,510 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,511 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:08:02,511 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,512 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:08:02,512 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,513 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:08:02,514 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:08:02,530 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:09:02,531 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:09:02
2025-07-07 12:09:02,923 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:09:02,924 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:09:03,190 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:09:03,357 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:09:03,357 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:09:03,771 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:09:03,772 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:09:03,772 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,773 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:09:03,773 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,774 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:09:03,774 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,775 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:09:03,775 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,777 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:09:03,777 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,779 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:09:03,779 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,780 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:09:03,780 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:09:03,796 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:10:03,798 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:10:03
2025-07-07 12:10:04,179 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:10:04,180 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:10:04,473 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:10:04,634 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:10:04,634 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:10:05,052 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:10:05,053 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:10:05,053 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,054 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:10:05,055 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,055 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:10:05,056 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,058 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:10:05,060 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,063 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:05,063 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,066 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:05,068 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,069 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:10:05,070 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:10:05,091 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:10:10,681 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:10:10,681 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:10:10,682 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:10:10,882 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:10:10,988 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:10:10,988 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:10:10,989 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:10:10
2025-07-07 12:10:11,335 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:10:11,607 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:10:11,607 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:10:11,863 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:10:11,976 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:10:11,976 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:10:12,381 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:10:12,381 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:10:12,381 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,385 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:10:12,385 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,387 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:10:12,387 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,390 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:10:12,390 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,391 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:12,392 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,393 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:12,393 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,394 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:10:12,395 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:10:12,438 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:11:12,453 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:11:12
2025-07-07 12:11:12,835 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:11:12,835 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:11:13,108 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:11:13,430 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:11:13,430 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:11:13,853 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:11:13,854 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:11:13,854 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,855 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:11:13,855 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,856 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:11:13,856 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,857 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:11:13,857 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,859 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:11:13,859 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,860 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:11:13,860 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,861 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:11:13,862 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:11:13,891 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:12:13,904 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:12:13
2025-07-07 12:12:14,279 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:12:14,279 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:12:14,527 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:12:14,727 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:12:14,728 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:12:15,148 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:12:15,148 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:12:15,148 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,149 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:12:15,150 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,150 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:12:15,151 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,153 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:12:15,153 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,155 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:12:15,155 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,156 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:12:15,157 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,158 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:12:15,159 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:12:15,188 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:13:15,195 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:13:15
2025-07-07 12:13:15,666 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:13:15,666 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:13:15,995 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:13:16,288 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:13:16,288 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:13:16,795 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:13:16,796 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:13:16,796 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,797 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:13:16,798 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,798 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:13:16,799 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,799 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:13:16,800 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,801 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:13:16,801 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,802 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:13:16,802 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,804 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:13:16,804 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:13:16,828 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:14:16,832 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:14:16
2025-07-07 12:14:17,197 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:14:17,197 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:14:17,450 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:14:17,621 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:14:17,621 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:14:18,027 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:14:18,027 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:14:18,028 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,029 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:14:18,030 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,032 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:14:18,032 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,034 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:14:18,035 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,037 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:14:18,038 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,039 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:14:18,040 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,042 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:14:18,042 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:14:18,069 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:24:39,587 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:24:39,588 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:24:39,588 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:24:41,006 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:24:41,058 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:24:41,059 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:24:41,059 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:24:41
2025-07-07 12:24:41,923 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:24:43,536 - src.data_providers.efinance_provider - INFO - 获取到 994 个期货行情数据
2025-07-07 12:24:43,536 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 12:24:43,546 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 12:24:43,547 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: property 'contract_month' of 'FuturesContract' object has no setter
2025-07-07 12:24:43,550 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:25:13,563 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:25:13
2025-07-07 12:25:15,212 - src.data_providers.efinance_provider - INFO - 获取到 994 个期货行情数据
2025-07-07 12:25:15,213 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 12:25:15,215 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 12:25:15,216 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: property 'contract_month' of 'FuturesContract' object has no setter
2025-07-07 12:25:15,218 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:25:45,227 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:25:45
2025-07-07 12:25:45,401 - src.data_providers.efinance_provider - INFO - 获取到 994 个期货行情数据
2025-07-07 12:25:45,402 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 12:25:45,404 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 12:25:45,405 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: property 'contract_month' of 'FuturesContract' object has no setter
2025-07-07 12:25:45,407 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:26:05,101 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:26:05,102 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:26:05,102 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:26:05,447 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:26:05,487 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:26:05,488 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:26:05,488 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:26:05
2025-07-07 12:26:05,681 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:26:05,742 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,743 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,766 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,768 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,789 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,789 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,825 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,825 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,845 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,845 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,866 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:26:06,074 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:26:36,079 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:26:36
2025-07-07 12:26:36,200 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,302 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,398 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,515 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,643 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,743 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:26:36,753 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:37:36,021 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:37:36,022 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:37:36,022 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:37:36,284 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:37:36,349 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:37:36,349 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:37:36,350 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:37:36
2025-07-07 12:37:36,389 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,433 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,454 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,477 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,512 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,554 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:37:36,554 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:37:54,415 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:37:54,415 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:37:54,415 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:37:54,594 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:37:54,633 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:37:54,634 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:37:54,634 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:37:54
2025-07-07 12:37:54,680 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,708 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,730 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,754 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,777 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,800 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:37:54,800 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:38:24,813 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:38:24
2025-07-07 12:38:24,944 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,066 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,166 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,280 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,401 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,530 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:38:25,531 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:38:55,536 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:38:55
2025-07-07 12:38:55,645 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:55,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:55,854 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:55,948 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:56,065 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:56,162 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:38:56,162 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:39:26,175 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:39:26
2025-07-07 12:39:26,274 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,398 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,493 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,594 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,718 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,847 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:39:26,847 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:39:56,853 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:39:56
2025-07-07 12:39:56,978 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,092 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,190 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,299 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,405 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,525 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:39:57,525 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:40:27,536 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:40:27
2025-07-07 12:40:27,658 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,759 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,881 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,907 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,950 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,996 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:40:27,997 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:40:58,008 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:40:58
2025-07-07 12:40:58,135 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,241 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,343 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,447 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,664 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,700 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,701 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,704 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,705 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,721 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,721 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,722 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,722 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,722 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,763 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,801 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,801 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,809 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,818 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,818 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,820 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,824 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,825 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,852 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,859 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,898 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,904 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,916 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,928 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,929 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,929 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,929 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,932 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,964 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,977 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,007 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,010 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,018 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,028 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,028 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,029 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,030 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,030 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,070 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,082 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,110 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,111 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,120 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,123 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,128 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,130 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,132 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,136 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,201 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,299 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:40:59,299 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:41:29,302 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:41:29
2025-07-07 12:41:29,411 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,522 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,628 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,737 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,842 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,948 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:41:29,949 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:41:59,954 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:41:59
2025-07-07 12:42:00,083 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,205 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,308 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,410 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,506 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,626 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:42:00,626 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:42:30,633 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:42:30
2025-07-07 12:42:30,758 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:30,892 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:30,994 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:31,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:31,197 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:31,318 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:42:31,318 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:43:01,330 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:43:01
2025-07-07 12:43:01,456 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,558 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,669 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,786 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,889 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:02,012 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:43:02,012 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 14:36:51,167 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 14:36:51,168 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 14:36:51,168 - __main__ - INFO - 验证数据源可用性...
2025-07-07 14:36:51,407 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 14:36:51,451 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 14:36:51,452 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 14:36:51,452 - __main__ - INFO - 开始数据更新 - 2025-07-07 14:36:51
2025-07-07 14:36:51,472 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,495 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,520 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,555 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,585 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,609 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:36:51,610 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 14:36:53,656 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,701 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,736 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,756 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,779 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,824 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:36:53,824 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 14:36:57,964 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,064 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,161 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,280 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,394 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,523 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:36:58,525 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 14:37:06,650 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:06,771 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:06,898 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:07,020 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:07,121 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:07,218 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:37:07,218 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:37:07,219 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 14:38:07,226 - __main__ - INFO - 开始数据更新 - 2025-07-07 14:38:07
2025-07-07 14:38:07,346 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,444 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,573 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,691 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,793 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,896 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:07,897 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 14:38:10,014 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,116 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,248 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,372 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,486 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,603 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:10,604 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 14:38:14,733 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:14,839 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:14,940 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:15,049 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:15,144 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:15,272 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:15,272 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 14:38:23,394 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,514 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,610 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,714 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,823 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,924 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:23,925 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:23,925 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 14:40:10,382 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 14:40:10,384 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 14:40:10,384 - __main__ - INFO - 验证数据源可用性...
2025-07-07 14:40:11,051 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 14:40:11,117 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 14:40:11,118 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 14:40:11,119 - __main__ - INFO - 开始数据更新 - 2025-07-07 14:40:11
2025-07-07 14:40:11,148 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,181 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,211 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,239 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,261 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,285 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:11,287 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 14:40:13,328 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,357 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,383 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,407 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,439 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,469 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:13,471 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 14:40:17,521 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,567 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,605 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,631 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,655 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,677 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:17,678 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 14:40:25,721 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,744 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,767 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,790 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,811 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,835 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:25,840 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:25,842 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 15:01:31,165 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 15:01:31,166 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 15:01:31,166 - __main__ - INFO - 验证数据源可用性...
2025-07-07 15:01:31,406 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 15:01:31,533 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 15:01:31,534 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 15:01:31,534 - __main__ - INFO - 开始数据更新 - 2025-07-07 15:01:31
2025-07-07 15:01:31,623 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:31,758 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:31,857 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:31,961 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:32,068 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:32,189 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:32,189 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 15:01:34,278 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,367 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,476 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,565 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,667 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,756 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:34,757 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 15:01:38,846 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:38,950 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,042 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,133 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,227 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,318 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:39,319 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 15:01:47,437 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,527 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,618 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,704 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,814 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,927 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:47,927 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:47,927 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 18:40:37,670 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 18:40:37,671 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 18:40:37,671 - __main__ - INFO - 验证数据源可用性...
2025-07-07 18:40:37,939 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 18:40:37,979 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 18:40:37,979 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 18:40:37,980 - __main__ - INFO - 开始数据更新 - 2025-07-07 18:40:37
2025-07-07 18:40:38,222 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 18:40:40,088 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-07 18:40:40,430 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-07 18:40:40,430 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-07 18:40:40,430 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 18:40:40,436 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 18:40:40,443 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-07 18:40:40,445 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-07 18:40:40,452 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-07 18:40:40,454 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-07 18:40:40,460 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-07 18:40:40,465 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-07 18:40:40,471 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-07 18:40:40,472 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-07 18:40:40,600 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 18:40:40,600 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 18:40:40,689 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 18:40:40,689 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,689 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,690 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,690 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,690 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,691 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,691 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,692 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,692 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,693 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,693 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,693 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,694 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,695 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,695 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,696 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,696 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,696 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,697 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,697 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,697 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,698 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,698 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,699 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,699 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,700 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,700 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,701 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,701 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,702 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,702 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,703 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,703 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-07 18:40:40,703 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,738 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4604 天
2025-07-07 18:40:40,739 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,739 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,739 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,740 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,740 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,741 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,741 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,758 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 2479 天
2025-07-07 18:40:40,759 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,759 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,760 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,761 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,761 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,762 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,763 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,800 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 5083 天
2025-07-07 18:40:40,800 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,802 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,803 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,804 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,804 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,805 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,806 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,849 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4372 天
2025-07-07 18:40:40,849 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,850 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,850 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,851 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,851 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,852 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,852 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-07 18:40:40,969 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-07 18:44:05,098 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 18:44:05,099 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 18:44:05,099 - __main__ - INFO - 验证数据源可用性...
2025-07-07 18:44:18,653 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-07 18:44:18,678 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 18:44:18,678 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-07 18:44:18,679 - __main__ - INFO - 开始数据更新 - 2025-07-07 18:44:18
2025-07-07 18:44:35,833 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 18:44:35,834 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-07 22:08:35,589 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 22:08:35,590 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 22:08:35,590 - __main__ - INFO - 验证数据源可用性...
2025-07-07 22:08:35,975 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-07 22:08:36,041 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 22:08:36,041 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-07 22:08:36,041 - __main__ - INFO - 开始数据更新 - 2025-07-07 22:08:36
2025-07-07 22:08:36,211 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 22:08:38,156 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-07 22:08:38,321 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-07 22:08:38,322 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-07 22:08:38,322 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 22:08:38,331 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 22:08:38,340 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-07 22:08:38,343 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-07 22:08:38,355 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-07 22:08:38,359 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-07 22:08:38,364 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-07 22:08:38,368 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-07 22:08:38,377 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-07 22:08:38,377 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-07 22:08:38,656 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-07 22:08:38,656 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,657 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,657 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,658 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,659 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,661 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,662 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,662 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,662 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,663 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,664 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,665 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,665 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,666 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,667 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,668 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,668 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,669 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,670 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,670 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,672 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,672 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,672 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,672 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,673 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,673 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,673 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,675 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,675 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,676 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,676 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,676 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,676 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-07 22:08:38,677 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,700 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4604 天
2025-07-07 22:08:38,701 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,702 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,703 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,704 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,705 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,706 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,706 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,719 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 2479 天
2025-07-07 22:08:38,720 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,721 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,722 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,723 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,723 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,724 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,724 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,751 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 5083 天
2025-07-07 22:08:38,753 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,753 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,753 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,754 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,754 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,755 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,755 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,779 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4372 天
2025-07-07 22:08:38,780 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,780 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,780 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,780 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,780 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,781 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,781 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-07 22:08:38,850 - __main__ - INFO - 等待 90 秒后下次更新...
