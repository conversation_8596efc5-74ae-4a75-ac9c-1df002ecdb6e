"""
金融股指期货监控系统数据模型
"""
from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Dict, List, Optional, Union
from enum import Enum
import pandas as pd


class FuturesType(Enum):
    """期货类型枚举"""
    IC = "IC"  # 中证500股指期货
    IM = "IM"  # 中证1000股指期货  
    IF = "IF"  # 沪深300股指期货
    IH = "IH"  # 上证50股指期货


class IndexType(Enum):
    """指数类型枚举"""
    CSI500 = "sh000905"    # 中证500指数
    CSI1000 = "sh000852"   # 中证1000指数
    CSI300 = "sh000300"    # 沪深300指数
    SSE50 = "sh000016"     # 上证50指数


@dataclass
class FuturesContract:
    """期货合约信息"""
    contract_code: str          # 合约代码，如IC2407
    product_code: str           # 品种代码，如IC
    contract_name: str          # 合约名称
    quote_id: str              # 行情ID，用于获取历史数据
    market_type: str           # 市场类型，如中金所
    expiry_date: Optional[date] = None  # 到期日期
    is_main_contract: bool = False      # 是否主力合约
    
    @property
    def contract_month(self) -> str:
        """获取合约月份，如2407"""
        if len(self.contract_code) >= 6:
            return self.contract_code[-4:]
        return ""


@dataclass
class FuturesQuote:
    """期货行情数据"""
    contract_code: str          # 合约代码
    timestamp: datetime         # 时间戳
    current_price: float        # 最新价
    open_price: float          # 开盘价
    high_price: float          # 最高价
    low_price: float           # 最低价
    volume: int                # 成交量
    amount: float              # 成交额
    prev_close: float          # 昨收价
    change_pct: float          # 涨跌幅
    change_amount: float       # 涨跌额


@dataclass
class IndexQuote:
    """指数行情数据"""
    index_code: str            # 指数代码
    index_name: str            # 指数名称
    timestamp: datetime        # 时间戳
    current_price: float       # 最新价
    open_price: float          # 开盘价
    high_price: float          # 最高价
    low_price: float           # 最低价
    volume: int                # 成交量
    amount: float              # 成交额
    prev_close: float          # 昨收价
    change_pct: float          # 涨跌幅
    change_amount: float       # 涨跌额


@dataclass
class BasisData:
    """基差数据"""
    futures_code: str          # 期货代码
    index_code: str            # 指数代码
    timestamp: datetime        # 时间戳
    futures_price: float       # 期货价格
    index_price: float         # 指数价格
    basis: float               # 基差 = 期货价格 - 指数价格
    basis_rate: float          # 基差率 = 基差 / 指数价格 * 100
    remaining_days: int        # 剩余交易日
    annualized_cost: float     # 年化基差成本


@dataclass
class SpreadData:
    """价差数据（不同月份合约间）"""
    near_contract: str         # 近月合约
    far_contract: str          # 远月合约
    timestamp: datetime        # 时间戳
    near_price: float          # 近月价格
    far_price: float           # 远月价格
    spread: float              # 价差 = 远月价格 - 近月价格
    spread_rate: float         # 价差率
    time_diff_days: int        # 时间差（交易日）
    annualized_time_cost: float # 年化时间成本


@dataclass
class CostMatrix:
    """成本矩阵"""
    timestamp: datetime                    # 时间戳
    basis_costs: Dict[str, BasisData]     # 基差成本数据，key为期货代码
    spread_costs: Dict[str, SpreadData]   # 价差成本数据，key为价差组合
    
    def to_dataframe(self) -> pd.DataFrame:
        """转换为DataFrame格式便于展示"""
        data = []
        
        # 添加基差成本数据
        for futures_code, basis_data in self.basis_costs.items():
            data.append({
                '类型': '基差成本',
                '合约组合': f"{futures_code} vs 现货",
                '期货代码': futures_code,
                '期货价格': basis_data.futures_price,
                '现货价格': basis_data.index_price,
                '基差': basis_data.basis,
                '基差率(%)': basis_data.basis_rate,
                '剩余天数': basis_data.remaining_days,
                '年化成本(%)': basis_data.annualized_cost
            })
        
        # 添加价差成本数据
        for spread_key, spread_data in self.spread_costs.items():
            data.append({
                '类型': '价差成本',
                '合约组合': f"{spread_data.near_contract} vs {spread_data.far_contract}",
                '期货代码': spread_data.near_contract,
                '期货价格': spread_data.near_price,
                '现货价格': spread_data.far_price,
                '基差': spread_data.spread,
                '基差率(%)': spread_data.spread_rate,
                '剩余天数': spread_data.time_diff_days,
                '年化成本(%)': spread_data.annualized_time_cost
            })
        
        return pd.DataFrame(data)


@dataclass
class MonitoringConfig:
    """监控配置"""
    futures_mapping: Dict[str, str] = field(default_factory=lambda: {
        'IC': 'sh000905',  # 中证500股指期货 -> 中证500指数
        'IM': 'sh000852',  # 中证1000股指期货 -> 中证1000指数
        'IF': 'sh000300',  # 沪深300股指期货 -> 沪深300指数
        'IH': 'sh000016',  # 上证50股指期货 -> 上证50指数
    })
    
    trading_days_per_year: int = 243       # 每年交易日天数
    update_interval: int = 60              # 数据更新间隔（秒）
    max_contracts_per_product: int = 6     # 每个品种最多监控的合约数
    enable_web_interface: bool = True      # 是否启用Web界面
    enable_data_persistence: bool = True   # 是否启用数据持久化
    log_level: str = "INFO"               # 日志级别


@dataclass
class SystemStatus:
    """系统状态"""
    last_update: datetime
    data_source_status: Dict[str, bool]    # 数据源状态
    active_contracts: List[str]            # 活跃合约列表
    error_count: int = 0                   # 错误计数
    warning_count: int = 0                 # 警告计数
